# AI Contact Manager - Environment Variables Example
# Copy this file to .env and fill in your values

# LLM Configuration for OCR Enhancement
# =====================================

# API Key for your LLM provider (required)
LLM_API_KEY=your_api_key_here

# Base URL for your LLM provider (optional, defaults to OpenAI)
# Examples:
# OpenAI: https://api.openai.com/v1
# OpenRouter: https://openrouter.ai/api/v1
# Together AI: https://api.together.xyz/v1
# Anthropic: https://api.anthropic.com
# Local/Custom: http://localhost:8000/v1
LLM_BASE_URL=https://api.openai.com/v1

# Model name (optional, defaults to gpt-3.5-turbo)
# Examples:
# OpenAI: gpt-3.5-turbo, gpt-4, gpt-4-turbo, gpt-4o, gpt-4o-mini
# Anthropic: claude-3-haiku-20240307, claude-3-sonnet-20240229, claude-3-5-sonnet-20241022
# OpenRouter: meta-llama/llama-3.1-8b-instruct, anthropic/claude-3-haiku
LLM_MODEL=gpt-3.5-turbo

# Maximum tokens for LLM responses (optional, defaults to 500)
LLM_MAX_TOKENS=500

# Temperature for LLM responses (optional, defaults to 0.1)
# Lower values (0.0-0.3) = more consistent, factual responses
# Higher values (0.7-1.0) = more creative responses
LLM_TEMPERATURE=0.1

# Security Notes:
# ===============
# - Never commit your actual .env file to version control
# - Keep your API keys secure and rotate them regularly
# - Use environment variables in production deployments
# - Consider using API key restrictions/scopes when available

# Deployment Examples:
# ===================
# 
# Netlify:
# Set environment variables in Site settings > Environment variables
# 
# Vercel:
# Set environment variables in Project settings > Environment Variables
# 
# Railway/Render:
# Set environment variables in your service configuration
# 
# Docker:
# Use --env-file flag: docker run --env-file .env your-image
