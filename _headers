/*
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net blob:; script-src-elem 'self' 'unsafe-inline' https://unpkg.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net blob:; worker-src 'self' blob: https://unpkg.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; connect-src 'self' https://unpkg.com https://cdn.jsdelivr.net https://api.openai.com https://api.anthropic.com https://openrouter.ai https://api.together.xyz https://api.groq.com https://api.cohere.ai https://*.openai.com https://*.anthropic.com https://*.together.xyz https://*.groq.com https://*.cohere.ai; img-src 'self' data: blob:; font-src 'self' data:; manifest-src 'self'; object-src 'none'
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
