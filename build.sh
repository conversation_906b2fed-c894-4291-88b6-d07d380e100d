#!/bin/bash

# Build script for AI Contact Manager
# This script prepares the static site for deployment

echo "🚀 Building AI Contact Manager..."

# Create build directory
mkdir -p dist

# Copy main files
echo "📁 Copying files..."
cp index.html dist/
cp app.js dist/
cp style.css dist/
cp netlify.toml dist/
cp _redirects dist/

# Copy additional files if they exist
if [ -f "favicon.ico" ]; then
    cp favicon.ico dist/
fi

if [ -f "robots.txt" ]; then
    cp robots.txt dist/
fi

if [ -f "sitemap.xml" ]; then
    cp sitemap.xml dist/
fi

# Create a simple manifest.json for PWA support
cat > dist/manifest.json << EOF
{
  "name": "AI Contact Manager",
  "short_name": "ContactManager",
  "description": "AI-powered contact management with business card scanning",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#2980b9",
  "icons": [
    {
      "src": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyIiBoZWlnaHQ9IjE5MiIgdmlld0JveD0iMCAwIDE5MiAxOTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxOTIiIGhlaWdodD0iMTkyIiByeD0iMjQiIGZpbGw9IiMyOTgwYjkiLz4KPHN2ZyB4PSI0OCIgeT0iNDgiIHdpZHRoPSI5NiIgaGVpZ2h0PSI5NiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiPgo8cGF0aCBkPSJtMyA5IDkgOSA5LTkiLz4KPC9zdmc+Cjwvc3ZnPgo=",
      "sizes": "192x192",
      "type": "image/svg+xml"
    }
  ]
}
EOF

# Add manifest link to HTML if not present
if ! grep -q "manifest.json" dist/index.html; then
    sed -i 's|</head>|    <link rel="manifest" href="manifest.json">\n</head>|' dist/index.html
fi

# Add viewport meta tag if not present
if ! grep -q "viewport" dist/index.html; then
    sed -i 's|</head>|    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n</head>|' dist/index.html
fi

echo "✅ Build complete! Files are in the 'dist' directory."
echo "📦 Ready for deployment to Netlify or any static hosting service."
echo ""
echo "To deploy:"
echo "1. Drag the 'dist' folder to Netlify"
echo "2. Or run: netlify deploy --dir=dist --prod"
echo ""
