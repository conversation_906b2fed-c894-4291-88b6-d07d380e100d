[build]
  # Build command (not needed for static sites, but can be used for preprocessing)
  command = "echo 'Building static site...'"
  
  # Directory to publish (current directory since files are in root)
  publish = "."

[build.environment]
  # Node.js version for any build processes
  NODE_VERSION = "18"

[[redirects]]
  # Handle client-side routing - redirect all routes to index.html
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  # Security headers for all pages
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net blob:; script-src-elem 'self' 'unsafe-inline' https://unpkg.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net blob:; worker-src 'self' blob: https://unpkg.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; connect-src 'self' https://unpkg.com https://cdn.jsdelivr.net https://api.openai.com https://api.anthropic.com https://openrouter.ai https://api.together.xyz https://api.groq.com https://api.cohere.ai https://*.openai.com https://*.anthropic.com https://*.together.xyz https://*.groq.com https://*.cohere.ai; img-src 'self' data: blob:; font-src 'self' data:; manifest-src 'self'; object-src 'none'"

[[headers]]
  # Cache static assets
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"
