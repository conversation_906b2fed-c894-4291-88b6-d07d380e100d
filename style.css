:root {
  /* Primitive Color Tokens */
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);
  --color-cream-50: rgba(252, 252, 249, 1);
  --color-cream-100: rgba(255, 255, 253, 1);
  --color-gray-200: rgba(245, 245, 245, 1);
  --color-gray-300: rgba(167, 169, 169, 1);
  --color-gray-400: rgba(119, 124, 124, 1);
  --color-slate-500: rgba(98, 108, 113, 1);
  --color-brown-600: rgba(94, 82, 64, 1);
  --color-charcoal-700: rgba(31, 33, 33, 1);
  --color-charcoal-800: rgba(38, 40, 40, 1);
  --color-slate-900: rgba(19, 52, 59, 1);
  --color-teal-300: rgba(50, 184, 198, 1);
  --color-teal-400: rgba(45, 166, 178, 1);
  --color-teal-500: rgba(33, 128, 141, 1);
  --color-teal-600: rgba(29, 116, 128, 1);
  --color-teal-700: rgba(26, 104, 115, 1);
  --color-teal-800: rgba(41, 150, 161, 1);
  --color-red-400: rgba(255, 84, 89, 1);
  --color-red-500: rgba(192, 21, 47, 1);
  --color-orange-400: rgba(230, 129, 97, 1);
  --color-orange-500: rgba(168, 75, 47, 1);

  /* RGB versions for opacity control */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  --color-slate-500-rgb: 98, 108, 113;
  --color-red-500-rgb: 192, 21, 47;
  --color-red-400-rgb: 255, 84, 89;
  --color-orange-500-rgb: 168, 75, 47;
  --color-orange-400-rgb: 230, 129, 97;

  /* Background color tokens (Light Mode) */
  --color-bg-1: rgba(59, 130, 246, 0.08); /* Light blue */
  --color-bg-2: rgba(245, 158, 11, 0.08); /* Light yellow */
  --color-bg-3: rgba(34, 197, 94, 0.08); /* Light green */
  --color-bg-4: rgba(239, 68, 68, 0.08); /* Light red */
  --color-bg-5: rgba(147, 51, 234, 0.08); /* Light purple */
  --color-bg-6: rgba(249, 115, 22, 0.08); /* Light orange */
  --color-bg-7: rgba(236, 72, 153, 0.08); /* Light pink */
  --color-bg-8: rgba(6, 182, 212, 0.08); /* Light cyan */

  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);
  --color-select-caret: rgba(var(--color-slate-900-rgb), 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  --font-family-mono: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco,
    Consolas, "Liberation Mono", "Courier New", monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    /* RGB versions for opacity control (Dark Mode) */
    --color-gray-400-rgb: 119, 124, 124;
    --color-teal-300-rgb: 50, 184, 198;
    --color-gray-300-rgb: 167, 169, 169;
    --color-gray-200-rgb: 245, 245, 245;

    /* Background color tokens (Dark Mode) */
    --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
    --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
    --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
    --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
    --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
    --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
    --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
    --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
    
    /* Semantic Color Tokens (Dark Mode) */
    --color-background: var(--color-charcoal-700);
    --color-surface: var(--color-charcoal-800);
    --color-text: var(--color-gray-200);
    --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
    --color-primary: var(--color-teal-300);
    --color-primary-hover: var(--color-teal-400);
    --color-primary-active: var(--color-teal-800);
    --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
    --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
    --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
    --color-border: rgba(var(--color-gray-400-rgb), 0.3);
    --color-error: var(--color-red-400);
    --color-success: var(--color-teal-300);
    --color-warning: var(--color-orange-400);
    --color-info: var(--color-gray-300);
    --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
    --color-btn-primary-text: var(--color-slate-900);
    --color-card-border: rgba(var(--color-gray-400-rgb), 0.2);
    --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: var(--color-teal-300-rgb);
    --color-error-rgb: var(--color-red-400-rgb);
    --color-warning-rgb: var(--color-orange-400-rgb);
    --color-info-rgb: var(--color-gray-300-rgb);
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  /* RGB versions for opacity control (dark mode) */
  --color-gray-400-rgb: 119, 124, 124;
  --color-teal-300-rgb: 50, 184, 198;
  --color-gray-300-rgb: 167, 169, 169;
  --color-gray-200-rgb: 245, 245, 245;

  /* Colorful background palette - Dark Mode */
  --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
  --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
  --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
  --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
  --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
  --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
  --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
  --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
  
  /* Semantic Color Tokens (Dark Mode) */
  --color-background: var(--color-charcoal-700);
  --color-surface: var(--color-charcoal-800);
  --color-text: var(--color-gray-200);
  --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
  --color-primary: var(--color-teal-300);
  --color-primary-hover: var(--color-teal-400);
  --color-primary-active: var(--color-teal-800);
  --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
  --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
  --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
  --color-border: rgba(var(--color-gray-400-rgb), 0.3);
  --color-error: var(--color-red-400);
  --color-success: var(--color-teal-300);
  --color-warning: var(--color-orange-400);
  --color-info: var(--color-gray-300);
  --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
  --color-btn-primary-text: var(--color-slate-900);
  --color-card-border: rgba(var(--color-gray-400-rgb), 0.15);
  --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
  --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: var(--color-teal-300-rgb);
  --color-error-rgb: var(--color-red-400-rgb);
  --color-warning-rgb: var(--color-orange-400-rgb);
  --color-info-rgb: var(--color-gray-300-rgb);
}

[data-color-scheme="light"] {
  /* RGB versions for opacity control (light mode) */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  
  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: var(--color-teal-500-rgb);
  --color-error-rgb: var(--color-red-500-rgb);
  --color-warning-rgb: var(--color-orange-500-rgb);
  --color-info-rgb: var(--color-slate-500-rgb);
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  /* Mobile touch optimizations */
  -webkit-overflow-scrolling: touch;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

*,
*::before,
*::after {
  box-sizing: inherit;
  /* Prevent text selection on touch devices for UI elements */
  -webkit-touch-callout: none;
}

/* Prevent zoom on input focus on mobile */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="search"],
textarea,
select {
  font-size: 16px; /* Prevents zoom on iOS */
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
  /* Mobile touch optimizations */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  -webkit-user-select: none;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
  /* Mobile touch optimizations */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

/* END PERPLEXITY DESIGN SYSTEM */
/* App-specific styles that extend the design system */

.app-container {
  display: flex;
  min-height: 100vh;
  background-color: var(--color-background);
}

/* Sidebar Navigation */
.sidebar {
  width: 280px;
  background-color: var(--color-surface);
  border-right: 1px solid var(--color-border);
  padding: var(--space-24);
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  overflow-y: auto;
  transition: transform var(--duration-normal) var(--ease-standard);
  z-index: 100;
}

.sidebar-header {
  margin-bottom: var(--space-32);
}

.sidebar-header h2 {
  font-size: var(--font-size-xl);
  color: var(--color-primary);
  font-weight: var(--font-weight-bold);
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav li {
  margin-bottom: var(--space-8);
}

.nav-btn {
  width: 100%;
  padding: var(--space-12) var(--space-16);
  border: none;
  background: transparent;
  color: var(--color-text);
  font-size: var(--font-size-base);
  text-align: left;
  border-radius: var(--radius-base);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  display: flex;
  align-items: center;
  gap: var(--space-12);
  /* Mobile touch optimizations */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  -webkit-user-select: none;
}

.nav-btn:hover {
  background-color: var(--color-secondary);
}

.nav-btn.active {
  background-color: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.nav-icon {
  font-size: var(--font-size-lg);
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 280px;
  padding: var(--space-32);
  min-height: 100vh;
}

.view {
  display: none;
}

.view.active {
  display: block;
}

.view-header {
  margin-bottom: var(--space-32);
}

.view-header h1 {
  margin-bottom: var(--space-8);
}

.view-header p {
  color: var(--color-text-secondary);
  margin: 0;
}

/* Dashboard Styles */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-24);
  margin-bottom: var(--space-32);
}

.stats-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-24);
}

.stats-content h3 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.stats-content p {
  color: var(--color-text-secondary);
  margin: 0;
}

.stats-icon {
  font-size: var(--font-size-4xl);
  opacity: 0.7;
}

.recent-contacts-section {
  margin-bottom: var(--space-32);
}

.contacts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-16);
  margin-top: var(--space-16);
}

.contact-card {
  padding: var(--space-20);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  /* Mobile touch optimizations */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  -webkit-user-select: none;
}

.contact-card:hover {
  transform: translateY(-2px);
}

.contact-header {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  margin-bottom: var(--space-12);
}

.contact-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  background: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-btn-primary-text);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
}

.contact-info h3 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--font-size-lg);
}

.contact-company {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
}

.contact-details p {
  margin: var(--space-4) 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.quick-actions {
  margin-bottom: var(--space-32);
}

.action-buttons {
  display: flex;
  gap: var(--space-16);
  flex-wrap: wrap;
}

.action-buttons .btn {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

/* Search and Filter Controls */
.search-controls {
  display: flex;
  gap: var(--space-16);
  align-items: center;
  margin-top: var(--space-16);
  flex-wrap: wrap;
}

.search-controls .form-control {
  min-width: 200px;
}

/* Form Styles */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-16);
  margin-bottom: var(--space-24);
}

.phone-entry, .email-entry {
  display: flex;
  gap: var(--space-8);
  align-items: center;
  margin-bottom: var(--space-8);
}

.phone-type, .email-type {
  width: 120px;
}

.phone-number, .email-address {
  flex: 1;
}

.add-phone, .add-email {
  width: 40px;
  padding: var(--space-6);
}

.address-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: var(--space-8);
  margin-top: var(--space-8);
}

.form-actions {
  display: flex;
  gap: var(--space-16);
  justify-content: flex-end;
  margin-top: var(--space-24);
  padding-top: var(--space-24);
  border-top: 1px solid var(--color-border);
}

/* Upload Area */
.upload-area {
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-32);
  text-align: center;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  /* Mobile touch optimizations */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  -webkit-user-select: none;
}

.upload-area:hover {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-teal-500-rgb), 0.05);
}

.upload-area.dragover {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-teal-500-rgb), 0.1);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-8);
}

.upload-icon {
  font-size: var(--font-size-4xl);
  opacity: 0.6;
}

.upload-link {
  color: var(--color-primary);
  cursor: pointer;
}

.upload-hint {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

/* Scan Results */
.scan-results {
  margin-top: var(--space-24);
}

.scan-results.hidden {
  display: none;
}

.extracted-data {
  background-color: var(--color-secondary);
  padding: var(--space-16);
  border-radius: var(--radius-base);
  margin: var(--space-16) 0;
}

.scan-actions {
  display: flex;
  gap: var(--space-12);
  flex-wrap: wrap;
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 4px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
  margin: var(--space-16) 0;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
  width: 0%;
  transition: width var(--duration-fast) var(--ease-standard);
  animation: progress-pulse 2s infinite;
}

.progress-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-top: var(--space-8);
  display: block;
  text-align: center;
}

@keyframes progress-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Import/Export Grid */
.import-export-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-24);
}

.import-area {
  margin: var(--space-16) 0;
}

.import-preview {
  margin-top: var(--space-16);
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  padding: var(--space-12);
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.export-actions {
  display: flex;
  gap: var(--space-12);
  flex-wrap: wrap;
}

/* Settings Grid */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-24);
}

.theme-selector {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.radio-label {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  cursor: pointer;
  padding: var(--space-8);
  border-radius: var(--radius-base);
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.radio-label:hover {
  background-color: var(--color-secondary);
}

.data-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal.hidden {
  display: none;
}

.modal-content {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-24);
  border-bottom: 1px solid var(--color-border);
}

.modal-header h2 {
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-3xl);
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: var(--space-4);
  border-radius: var(--radius-base);
  line-height: 1;
}

.modal-close:hover {
  background-color: var(--color-secondary);
}

.modal-body {
  padding: var(--space-24);
}

.modal-actions {
  display: flex;
  gap: var(--space-12);
  justify-content: flex-end;
  padding: var(--space-24);
  border-top: 1px solid var(--color-border);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-overlay.hidden {
  display: none;
}

.loading-content {
  text-align: center;
  color: var(--color-white);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--color-white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-16);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Toast Notifications */
#toast-container {
  position: fixed;
  top: var(--space-24);
  right: var(--space-24);
  z-index: 1500;
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.toast {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  padding: var(--space-16);
  box-shadow: var(--shadow-lg);
  min-width: 300px;
  animation: slideIn var(--duration-normal) var(--ease-standard);
}

.toast.success {
  border-color: var(--color-success);
  background-color: rgba(var(--color-success-rgb), 0.1);
}

.toast.error {
  border-color: var(--color-error);
  background-color: rgba(var(--color-error-rgb), 0.1);
}

.toast.warning {
  border-color: var(--color-warning);
  background-color: rgba(var(--color-warning-rgb), 0.1);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  position: fixed;
  top: var(--space-16);
  left: var(--space-16);
  z-index: 1001;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  padding: var(--space-8);
  width: 44px;
  height: 44px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.mobile-menu-toggle:hover {
  background: var(--color-surface-hover);
  transform: scale(1.05);
}

.mobile-menu-toggle:active {
  transform: scale(0.95);
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background: var(--color-text);
  border-radius: 1px;
  transition: all var(--transition-fast);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Overlay */
.mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.mobile-overlay.active {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: flex;
  }

  .mobile-overlay {
    display: block;
  }

  .sidebar {
    transform: translateX(-100%);
    width: 280px;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
    padding: var(--space-16);
    padding-top: 80px; /* Account for mobile menu button */
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .address-row {
    grid-template-columns: 1fr;
  }
  
  .search-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-controls .form-control {
    min-width: unset;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .import-export-grid,
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    width: 95%;
    margin: var(--space-16);
  }
  
  .contacts-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--space-12);
    padding-top: 70px;
  }

  .view-header h1 {
    font-size: var(--font-size-3xl);
  }

  .phone-entry,
  .email-entry {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-8);
  }

  .phone-type,
  .email-type {
    width: 100%;
    min-height: 44px; /* Touch-friendly minimum */
  }

  .form-actions {
    flex-direction: column;
    gap: var(--space-12);
  }

  .scan-actions,
  .export-actions {
    flex-direction: column;
    gap: var(--space-12);
  }

  .modal-actions {
    flex-direction: column;
    gap: var(--space-12);
  }

  /* Touch-friendly buttons */
  .btn {
    min-height: 44px;
    padding: var(--space-12) var(--space-16);
    font-size: var(--font-size-base);
  }

  .btn--sm {
    min-height: 36px;
    padding: var(--space-8) var(--space-12);
  }

  /* Larger form controls for mobile */
  .form-control {
    min-height: 44px;
    padding: var(--space-12);
    font-size: var(--font-size-base);
  }

  /* Better upload area for mobile */
  .upload-area {
    min-height: 200px;
    padding: var(--space-24);
  }

  .upload-content h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-12);
  }

  .upload-icon {
    font-size: 3rem;
    margin-bottom: var(--space-16);
  }

  /* Mobile-optimized contact cards */
  .contact-card {
    padding: var(--space-16);
  }

  .contact-card h3 {
    font-size: var(--font-size-lg);
  }

  /* Better modal for mobile */
  .modal-content {
    width: 95vw;
    max-width: none;
    margin: var(--space-8);
    max-height: 90vh;
    overflow-y: auto;
  }

  /* Sidebar improvements for mobile */
  .sidebar {
    width: 85vw;
    max-width: 320px;
  }

  .sidebar-nav button {
    padding: var(--space-16);
    font-size: var(--font-size-base);
    min-height: 48px;
  }
}

/* Touch and Mobile Optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Touch device optimizations */
  .btn:hover {
    transform: none;
  }

  .btn:active {
    transform: scale(0.98);
    background: var(--color-primary-dark);
  }

  .btn--outline:active {
    background: var(--color-surface-hover);
  }

  .contact-card:hover {
    transform: none;
  }

  .contact-card:active {
    transform: scale(0.98);
    background: var(--color-surface-hover);
  }

  .nav-btn:hover {
    background: var(--color-surface);
  }

  .nav-btn:active {
    background: var(--color-surface-hover);
    transform: scale(0.98);
  }

  .upload-area:hover {
    transform: none;
  }

  .upload-area:active {
    background: var(--color-primary-50);
    border-color: var(--color-primary);
    transform: scale(0.99);
  }

  /* Improve form control touch targets */
  .form-control {
    min-height: 44px;
    padding: var(--space-12) var(--space-16);
  }

  /* Better touch feedback for interactive elements */
  [role="button"],
  button,
  .btn,
  .nav-btn,
  .contact-card,
  .upload-area {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }

  /* Touch active state for enhanced feedback */
  .touch-active {
    transform: scale(0.98) !important;
    opacity: 0.8 !important;
    transition: all 0.1s ease !important;
  }

  /* Add touch feedback for interactive elements */
  .form-control:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }

  .modal-close:active,
  .camera-close:active {
    background: var(--color-surface-hover);
    transform: scale(0.95);
  }
}

/* Prevent zoom on input focus for iOS */
@media screen and (max-width: 768px) {
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="url"],
  textarea,
  select {
    font-size: 16px !important;
  }
}

/* Better camera capture experience */
.upload-area {
  position: relative;
  overflow: hidden;
}

.upload-area.dragover {
  background: var(--color-primary-50);
  border-color: var(--color-primary);
}

.upload-link {
  color: var(--color-primary);
  cursor: pointer;
  text-decoration: underline;
}

.upload-link:hover {
  color: var(--color-primary-dark);
}

/* Camera preview styles */
.camera-preview {
  width: 100%;
  max-width: 400px;
  border-radius: var(--border-radius-md);
  margin: var(--space-16) 0;
}

/* OCR Progress improvements */
.progress-bar {
  background: var(--color-surface);
  border-radius: var(--border-radius-full);
  overflow: hidden;
  height: 8px;
  margin: var(--space-16) 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  transition: width 0.3s ease;
  border-radius: var(--border-radius-full);
}

.progress-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  text-align: center;
  margin-top: var(--space-8);
}

/* Scan fallback styles */
.scan-fallback {
  text-align: center;
  padding: var(--space-24);
  background: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 2px dashed var(--color-border);
  margin: var(--space-16) 0;
}

.scan-fallback p {
  margin-bottom: var(--space-16);
  color: var(--color-text-secondary);
}

.fallback-image {
  margin-bottom: var(--space-16);
}

.fallback-image img {
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border);
}

.fallback-actions {
  display: flex;
  gap: var(--space-12);
  justify-content: center;
  flex-wrap: wrap;
}

/* Manual extraction styles */
.manual-extraction {
  text-align: left;
  max-width: 600px;
  margin: 0 auto;
}

.extraction-image {
  text-align: center;
  margin-bottom: var(--space-24);
}

.extraction-form h3 {
  margin-bottom: var(--space-8);
  color: var(--color-text);
}

.extraction-form p {
  margin-bottom: var(--space-16);
  color: var(--color-text-secondary);
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-12);
  margin-bottom: var(--space-24);
}

.extraction-actions {
  display: flex;
  gap: var(--space-12);
  justify-content: center;
}

/* LLM Configuration Styles */
.llm-status {
  font-size: var(--font-size-sm);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--border-radius-sm);
  font-weight: 500;
}

.llm-status.enabled {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.llm-status.disabled {
  background: var(--color-border);
  color: var(--color-text-secondary);
}

.llm-config {
  margin-top: var(--space-16);
}

.llm-config .form-group {
  margin-bottom: var(--space-16);
}

.llm-config label {
  display: block;
  margin-bottom: var(--space-4);
  font-weight: 500;
  color: var(--color-text);
}

.llm-actions {
  display: flex;
  gap: var(--space-12);
  margin-top: var(--space-20);
  flex-wrap: wrap;
}

.llm-info {
  margin-top: var(--space-12);
  padding: var(--space-12);
  background: var(--color-surface);
  border-radius: var(--border-radius-md);
  border-left: 3px solid var(--color-primary);
}

.llm-info small {
  color: var(--color-text-secondary);
  line-height: 1.4;
}

.form-help {
  display: block;
  margin-top: var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: 1.4;
}

@media (max-width: 480px) {
  .fallback-actions {
    flex-direction: column;
  }

  .fallback-actions .btn {
    width: 100%;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .extraction-actions {
    flex-direction: column;
  }

  .extraction-actions .btn {
    width: 100%;
  }

  .llm-actions {
    flex-direction: column;
  }

  .llm-actions .btn {
    width: 100%;
  }
}

/* Camera Modal Styles */
.camera-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
}

.camera-content {
  background: var(--color-surface);
  border-radius: var(--border-radius-lg);
  padding: var(--space-24);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
}

.camera-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-16);
}

.camera-header h3 {
  margin: 0;
  color: var(--color-text);
}

.camera-close {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-md);
}

.camera-close:hover {
  background: var(--color-surface-hover);
  color: var(--color-text);
}

.camera-video {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius-md);
  background: var(--color-background);
  margin-bottom: var(--space-16);
}

.camera-controls {
  display: flex;
  gap: var(--space-12);
  justify-content: center;
}

.camera-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-8);
}

@media (max-width: 480px) {
  .camera-content {
    padding: var(--space-16);
    margin: var(--space-8);
  }

  .camera-controls {
    flex-direction: column;
  }

  .camera-controls .btn {
    width: 100%;
  }
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.hidden {
  display: none !important;
}

.mb-16 {
  margin-bottom: var(--space-16);
}

.mt-16 {
  margin-top: var(--space-16);
}

.full-width {
  width: 100%;
}

/* Contact Detail Styles */
.contact-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-16);
}

.contact-field {
  margin-bottom: var(--space-12);
}

.contact-field-label {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
}

.contact-field-value {
  font-size: var(--font-size-base);
  color: var(--color-text);
}

.contact-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  margin-top: var(--space-8);
}

.tag {
  background-color: var(--color-primary);
  color: var(--color-btn-primary-text);
  padding: var(--space-2) var(--space-8);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: var(--space-32);
  color: var(--color-text-secondary);
}

.empty-state-icon {
  font-size: var(--font-size-4xl);
  opacity: 0.5;
  margin-bottom: var(--space-16);
}

.empty-state h3 {
  margin-bottom: var(--space-8);
  font-size: var(--font-size-xl);
}

.empty-state p {
  margin-bottom: var(--space-16);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}